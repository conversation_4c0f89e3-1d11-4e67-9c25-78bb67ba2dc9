.footer {
  background: #000;
  color: #fff;
  padding: 80px 0 0;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 60px;
}

.footer-section h3 {
  font-size: 1.8rem;
  font-weight: 300;
  letter-spacing: 2px;
  margin-bottom: 20px;
  text-transform: uppercase;
}

.footer-section h4 {
  font-size: 1rem;
  font-weight: 400;
  letter-spacing: 1px;
  margin-bottom: 20px;
  text-transform: uppercase;
  color: #fff;
}

.footer-section p {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #ccc;
  margin-bottom: 20px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 12px;
}

.footer-section ul li a {
  color: #ccc;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #fff;
}

/* Social Links */
.social-links {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.social-links a {
  color: #ccc;
  transition: color 0.3s ease;
}

.social-links a:hover {
  color: #fff;
}

/* Newsletter */
.newsletter-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-width: 300px;
}

.newsletter-input {
  padding: 12px 15px;
  border: 1px solid #333;
  background: transparent;
  color: #fff;
  font-size: 0.9rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.newsletter-input::placeholder {
  color: #666;
}

.newsletter-input:focus {
  border-color: #fff;
}

.newsletter-btn {
  padding: 12px 20px;
  background: #fff;
  color: #000;
  border: none;
  font-size: 0.9rem;
  font-weight: 400;
  letter-spacing: 1px;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.3s ease;
}

.newsletter-btn:hover {
  background: #ccc;
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid #333;
  padding: 30px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-bottom p {
  color: #666;
  font-size: 0.8rem;
  margin: 0;
}

.footer-links {
  display: flex;
  gap: 30px;
}

.footer-links a {
  color: #666;
  text-decoration: none;
  font-size: 0.8rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #fff;
}

/* Responsive */
@media (max-width: 768px) {
  .footer {
    padding: 60px 0 0;
  }
  
  .footer-content {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
  }
  
  .footer-section h3 {
    font-size: 1.5rem;
    letter-spacing: 1px;
  }
  
  .newsletter-form {
    max-width: 100%;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-links {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 25px;
  }
  
  .social-links {
    justify-content: center;
  }
  
  .footer-links {
    flex-direction: column;
    gap: 15px;
  }
}
