.hero {
  margin-top: 80px; /* Account for fixed header */
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.hero-content {
  height: 100%;
  position: relative;
}

.hero-image {
  position: relative;
  height: 100%;
  width: 100%;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-text {
  text-align: center;
  color: #fff;
  max-width: 600px;
  padding: 0 20px;
}

.hero-text h1 {
  font-size: 4rem;
  font-weight: 200;
  letter-spacing: 2px;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.hero-text p {
  font-size: 1.2rem;
  font-weight: 300;
  letter-spacing: 1px;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.hero-buttons .btn {
  min-width: 150px;
  padding: 15px 30px;
  font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 768px) {
  .hero {
    margin-top: 60px;
    height: 80vh;
  }
  
  .hero-text h1 {
    font-size: 2.5rem;
    letter-spacing: 1px;
  }
  
  .hero-text p {
    font-size: 1rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-buttons .btn {
    width: 200px;
  }
}

@media (max-width: 480px) {
  .hero-text h1 {
    font-size: 2rem;
  }
  
  .hero-text p {
    font-size: 0.9rem;
  }
  
  .hero-buttons .btn {
    padding: 12px 24px;
    width: 180px;
  }
}
