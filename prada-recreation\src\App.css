/* Prada-inspired CSS */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #000;
  background-color: #fff;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 300;
  letter-spacing: 0.5px;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
  font-weight: 200;
}

h2 {
  font-size: 2rem;
  font-weight: 300;
}

h3 {
  font-size: 1.5rem;
  font-weight: 400;
}

p {
  font-size: 1rem;
  line-height: 1.8;
  margin-bottom: 1rem;
}

/* Buttons */
.btn {
  background: transparent;
  border: 1px solid #000;
  padding: 12px 24px;
  font-size: 0.9rem;
  font-weight: 400;
  letter-spacing: 1px;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  color: #000;
}

.btn:hover {
  background: #000;
  color: #fff;
}

.btn-white {
  border-color: #fff;
  color: #fff;
}

.btn-white:hover {
  background: #fff;
  color: #000;
}

/* Layout utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section {
  padding: 80px 0;
}

.text-center {
  text-align: center;
}

.mb-4 {
  margin-bottom: 2rem;
}

.mb-8 {
  margin-bottom: 4rem;
}

/* Grid system */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }

  h1 {
    font-size: 2rem;
  }

  .section {
    padding: 40px 0;
  }
}
