.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  z-index: 1000;
  transition: all 0.3s ease;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

/* Logo */
.logo {
  font-size: 1.8rem;
  font-weight: 300;
  letter-spacing: 3px;
}

.logo a {
  color: #000;
  text-decoration: none;
}

/* Desktop Navigation */
.nav-desktop {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav-list li a {
  color: #000;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 400;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  transition: opacity 0.3s ease;
  position: relative;
}

.nav-list li a:hover {
  opacity: 0.7;
}

.nav-list li a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 1px;
  background: #000;
  transition: width 0.3s ease;
}

.nav-list li a:hover::after {
  width: 100%;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-btn,
.account-btn,
.cart-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  color: #000;
  transition: opacity 0.3s ease;
}

.search-btn:hover,
.account-btn:hover,
.cart-btn:hover {
  opacity: 0.7;
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
}

.hamburger {
  display: flex;
  flex-direction: column;
  width: 20px;
  height: 16px;
  justify-content: space-between;
}

.hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background: #000;
  transition: all 0.3s ease;
}

.hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Navigation */
.nav-mobile {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.nav-mobile.active {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.nav-mobile-list {
  list-style: none;
  margin: 0;
  padding: 20px;
}

.nav-mobile-list li {
  border-bottom: 1px solid #f0f0f0;
}

.nav-mobile-list li:last-child {
  border-bottom: none;
}

.nav-mobile-list li a {
  display: block;
  padding: 15px 0;
  color: #000;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 400;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  transition: opacity 0.3s ease;
}

.nav-mobile-list li a:hover {
  opacity: 0.7;
}

/* Responsive */
@media (max-width: 768px) {
  .nav-desktop {
    display: none;
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .header-container {
    height: 60px;
  }
  
  .logo {
    font-size: 1.5rem;
    letter-spacing: 2px;
  }
  
  .header-actions {
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 15px;
  }
  
  .logo {
    font-size: 1.3rem;
  }
}
