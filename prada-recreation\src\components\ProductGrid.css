.product-grid-section {
  padding: 100px 0;
  background: #fff;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 200;
  letter-spacing: 1px;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.section-header p {
  font-size: 1.1rem;
  color: #666;
  font-weight: 300;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-bottom: 60px;
}

.product-card {
  background: #fff;
  transition: transform 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-image {
  position: relative;
  aspect-ratio: 3/4;
  overflow: hidden;
  margin-bottom: 20px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.product-overlay .btn {
  background: #fff;
  color: #000;
  border: none;
  padding: 12px 24px;
  font-size: 0.9rem;
  font-weight: 400;
  letter-spacing: 1px;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.3s ease;
}

.product-overlay .btn:hover {
  background: #000;
  color: #fff;
}

.product-info {
  text-align: center;
}

.product-category {
  font-size: 0.8rem;
  color: #999;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: block;
  margin-bottom: 8px;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 400;
  margin-bottom: 8px;
  color: #000;
}

.product-price {
  font-size: 1rem;
  font-weight: 500;
  color: #000;
  margin: 0;
}

.section-footer {
  text-align: center;
}

.section-footer .btn {
  padding: 15px 40px;
  font-size: 0.9rem;
  min-width: 200px;
}

/* Responsive */
@media (max-width: 768px) {
  .product-grid-section {
    padding: 60px 0;
  }
  
  .section-header {
    margin-bottom: 40px;
  }
  
  .section-header h2 {
    font-size: 2rem;
  }
  
  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
  }
  
  .product-overlay {
    opacity: 1;
    background: rgba(0, 0, 0, 0.3);
  }
}

@media (max-width: 480px) {
  .product-grid-section {
    padding: 40px 0;
  }
  
  .section-header h2 {
    font-size: 1.8rem;
  }
  
  .product-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .section-footer .btn {
    width: 100%;
    max-width: 300px;
  }
}
